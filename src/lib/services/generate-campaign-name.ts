import dotenv from 'dotenv';
import { Langfuse } from "langfuse";
import { callLLM } from '../utils/callLLM.js';

dotenv.config();

const langfuse = new Langfuse({
  secretKey: process.env.LANGFUSE_SECRET_KEY,
  publicKey: process.env.LANGFUSE_PUBLIC_KEY,
  baseUrl: process.env.LANGFUSE_BASE_URL
});

export const generateCampaignName = async (campaignInformation: string): Promise<string> => {
  console.log('campaignInformation', campaignInformation);

  try {
    // Get production prompt 
    const prompt: any = await langfuse.getPrompt("generate-campaign-name", undefined, { label: "latest" })
    const compiledPrompt = prompt.compile({
        campaign_information: campaignInformation
    });
    console.log(compiledPrompt);
    console.log(prompt);
    const response = await callLLM(compiledPrompt, prompt, prompt.config.model, { 
        parse: false,
        temperature: prompt.config.temperature
    });
    return response;

  } catch (error) {
    console.error('Error generating campaign name:', error);
    throw error;
  }
};
