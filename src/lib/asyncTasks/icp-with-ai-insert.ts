import postgres from 'postgres';
import { generateContentSchedule } from '../services/generate-schedule.js';
import { generateCompanyBrand } from '../services/generate_company_brand.js';
import { ICPInsert } from '../types/icp-insert.js';
import { generateICP } from '../services/generate-icp.js';
import { scrapeLinkedInProfile } from '../utils/scrape-linkedin-profiles.js';
import { generateICPLinkedIn } from '../services/generate-icp-linkedin.js';

export async function icpWithAiInsert(sql: any, {
    id,
    values
}: {
    id: string;
    values: ICPInsert;
}) {

    const accountResult = await sql`
        SELECT website FROM accounts WHERE id = ${values.company_id}
    `;
    const accountData = accountResult[0];

    const website = accountData?.website;

    const referenceProducts = await sql`
    SELECT name, description, key_features, target_audience FROM products WHERE id = ANY(${values.reference_products})
`;

    const referenceMaterialContent = referenceProducts.map(product => {
        const features = Array.isArray(product.key_features) 
            ? product.key_features.map(f => `${f.name}: ${f.value_prop} (${f.differentiation})`).join(', ')
            : '';
        return `Product: ${product.name}\nDescription: ${product.description || 'N/A'}\nKey Features: ${features}`;
    }).join('\n\n');
    if(!values.withLinkedIn) {
        try {
            const previousICPData = await sql`
                SELECT * FROM icps WHERE company_id = ${values.company_id}
            `;

            const icpData = await generateICP(website, accountData.name, previousICPData, referenceMaterialContent, values.reference_description);

            await sql`
            UPDATE icps 
            SET 
                data = ${sql.json(icpData as any || {})},
                name = ${icpData?.name || ''},
                is_generating = false,
                updated_at = ${Date.now()}
            WHERE id = ${id}
            `;
        } catch (error) {
            console.error('Failed to generate ICP:', error);
            
            await sql`
                UPDATE icps 
                SET 
                is_generating = false,
                error_generating = true,
                updated_at = ${Date.now()}
                WHERE id = ${id}
            `;
        }
      } else if(values.withAi && values.withLinkedIn) {
          try {
            const userProfiles = await Promise.all(values.linkedInUrls.map(async (user) => {
                console.log('user', user);
                const profile = await scrapeLinkedInProfile('', sql, true, user);
                if(!profile) {
                  return null;
                }
                return profile;
              }));

            const icpData = await generateICPLinkedIn(
                website, 
                accountData.name, 
                userProfiles, 
                referenceMaterialContent, 
                values.reference_description
            );
            console.log('icpData', icpData);
            await sql`
            UPDATE icps 
            SET 
                data = ${sql.json(icpData as any || {})},
                name = ${icpData.name},
                is_generating = false,
                updated_at = ${Date.now()}
            WHERE id = ${id}
            `;
          } catch (error) {
            console.error('Failed to generate ICP:', error);
            await sql`
              UPDATE icps 
              SET 
                is_generating = false,
                error_generating = true,
                updated_at = ${Date.now()}
              WHERE id = ${id}
            `;
          }
      }

} 