{"id": "sb-server-env", "name": "SB Server Environment", "values": [{"key": "base_url", "value": "http://localhost:8080", "type": "default", "description": "Base URL for the SB Server API"}, {"key": "production_base_url", "value": "https://your-production-domain.com", "type": "default", "description": "Production URL for the SB Server API"}, {"key": "api_token", "value": "your-bearer-token-here", "type": "secret", "description": "Bearer token for API authentication"}, {"key": "firecrawl_api_key", "value": "fc-your-firecrawl-api-key", "type": "secret", "description": "Firecrawl API key for website crawling"}, {"key": "apify_api_token", "value": "apify-your-token-here", "type": "secret", "description": "Apify API token for social media scraping"}, {"key": "jigsaw_api_key", "value": "jigsaw-your-api-key", "type": "secret", "description": "JigsawStack API key for AI scraping"}, {"key": "langfuse_secret_key", "value": "lf-secret-key-here", "type": "secret", "description": "Langfuse secret key for LLM observability"}, {"key": "langfuse_public_key", "value": "lf-public-key-here", "type": "default", "description": "Langfuse public key for LLM observability"}, {"key": "langfuse_base_url", "value": "https://cloud.langfuse.com", "type": "default", "description": "Langfuse base URL"}, {"key": "zero_upstream_db", "value": "postgresql://user:password@localhost:5432/sbserver", "type": "secret", "description": "PostgreSQL connection string for Zero upstream database"}, {"key": "timestamp", "value": "", "type": "default", "description": "Dynamic timestamp set by pre-request scripts"}, {"key": "last_crawl_id", "value": "", "type": "default", "description": "Last crawl ID returned by crawl endpoint"}, {"key": "last_extract_id", "value": "", "type": "default", "description": "Last extract ID returned by extract endpoint"}, {"key": "sample_website_url", "value": "https://smartberry.ai", "type": "default", "description": "Sample website URL for testing scraping endpoints"}, {"key": "sample_company_domain", "value": "smartberry.ai", "type": "default", "description": "Sample company domain for testing"}], "_postman_variable_scope": "environment"}