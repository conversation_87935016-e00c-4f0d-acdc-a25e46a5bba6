# SB Server Postman Collection Setup Guide

This guide will help you set up and use the Postman collection for the SB Server API with all the test data and environment variables.

## 📋 Files Included

- `sb-server-postman-collection.json` - Complete API collection with test data
- `sb-server-postman-environment.json` - Environment variables and configuration
- `POSTMAN_SETUP.md` - This setup guide

## 🚀 Quick Setup

### 1. Import Collection and Environment

1. Open Postman
2. Click **Import** button
3. Select both JSON files:
   - `sb-server-postman-collection.json`
   - `sb-server-postman-environment.json`
4. Click **Import**

### 2. Configure Environment Variables

After importing, go to **Environments** and select "SB Server Environment". Update these critical variables:

#### Required API Keys
```
firecrawl_api_key = fc-your-actual-api-key-here
apify_api_token = apify-your-actual-token-here
jigsaw_api_key = jigsaw-your-actual-api-key-here
langfuse_secret_key = lf-your-secret-key-here
```

#### Database Configuration
```
zero_upstream_db = postgresql://user:password@localhost:5432/sbserver
```

#### Server URLs
```
base_url = http://localhost:8080  (for local development)
production_base_url = https://your-production-domain.com
```

### 3. Select Environment

Make sure to select the "SB Server Environment" from the environment dropdown in the top right of Postman.

## 📝 API Endpoints Overview

### Health & Testing
- **GET** `/` - Health check
- **GET** `/env-test` - Environment configuration test

### Data Management
- **POST** `/push` - Push data mutations to database

### Website Crawling & Extraction
- **GET** `/crawl` - Start website crawling
- **GET** `/crawl-status` - Check crawl job status
- **POST** `/extract-from-website` - Extract structured data with AI
- **GET** `/extract-status` - Check extraction job status

### Website Scraping
- **GET** `/scrape` - Basic website scraping
- **POST** `/scrape-by-element` - Advanced AI-powered element scraping

### AI Content Generation
- **POST** `/generate-schedule` - Generate content marketing schedule
- **POST** `/generate-persona` - Create buyer personas
- **POST** `/generate-visual-description` - Generate visual content descriptions

### Social Media Scraping
- **POST** `/scrape/reddit` - Reddit content scraping
- **POST** `/scrape/tiktok` - TikTok content scraping  
- **POST** `/scrape/twitter` - Twitter/X content scraping

## 🧪 Sample Test Data

Each endpoint includes realistic test data:

### Website Crawling Example
```json
{
  "url": "https://smartberry.ai",
  "limit": 50
}
```

### Content Schedule Generation Example
```json
{
  "productDocumentation": "Our SaaS platform helps businesses automate their marketing workflows...",
  "campaignGoal": "Increase brand awareness and generate 500 new leads...",
  "startDate": "2024-09-01",
  "endDate": "2024-11-30"
}
```

### Social Media Scraping Example
```json
{
  "keywords": "marketing automation SaaS startup",
  "timeFilter": "month",
  "includeComments": true,
  "maxItems": 100
}
```

## ⚙️ Environment Variables Reference

| Variable | Type | Description | Example |
|----------|------|-------------|---------|
| `base_url` | Default | Local server URL | `http://localhost:8080` |
| `production_base_url` | Default | Production server URL | `https://api.example.com` |
| `api_token` | Secret | Bearer authentication token | `bearer-token-here` |
| `firecrawl_api_key` | Secret | Firecrawl service API key | `fc-key-here` |
| `apify_api_token` | Secret | Apify scraping service token | `apify-token-here` |
| `jigsaw_api_key` | Secret | JigsawStack API key | `jigsaw-key-here` |
| `langfuse_secret_key` | Secret | Langfuse LLM observability key | `lf-secret-here` |
| `zero_upstream_db` | Secret | PostgreSQL connection string | `********************************/db` |

## 🔧 Pre-request and Test Scripts

The collection includes:

### Pre-request Scripts
- Dynamic timestamp generation
- Request logging for debugging
- Environment variable validation

### Test Scripts  
- Response status validation
- Response time checks
- JSON structure validation
- Automatic variable extraction (crawl IDs, extract IDs)

## 🔍 Testing Workflows

### 1. Basic Health Check
1. Run **Health Check** (`GET /`)
2. Run **Environment Test** (`GET /env-test`)

### 2. Website Analysis Workflow
1. **Crawl Website** (`GET /crawl`) with target URL
2. **Get Crawl Status** (`GET /crawl-status`) using returned crawl ID
3. **Extract from Website** (`POST /extract-from-website`) for structured data
4. **Get Extract Status** (`GET /extract-status`) using returned extract ID

### 3. Content Generation Workflow
1. **Generate Persona** (`POST /generate-persona`) with ICP data
2. **Generate Content Schedule** (`POST /generate-schedule`) for campaign
3. **Generate Visual Description** (`POST /generate-visual-description`) for content

### 4. Social Media Research Workflow
1. **Scrape Reddit** (`POST /scrape/reddit`) for trending topics
2. **Scrape TikTok** (`POST /scrape/tiktok`) for viral content
3. **Scrape Twitter** (`POST /scrape/twitter`) for competitor analysis

## 🚨 Troubleshooting

### Common Issues

1. **401 Unauthorized**
   - Check API keys in environment variables
   - Verify bearer token is set correctly

2. **500 Internal Server Error**
   - Verify database connection string
   - Check server logs for specific error details

3. **Timeout Errors**
   - Increase timeout in Postman settings
   - Check if external APIs (Firecrawl, Apify) are responsive

4. **Invalid JSON Response**
   - Verify content-type headers
   - Check request body format matches expected schema

### Debug Tips

- Enable Postman Console (`View > Show Postman Console`) to see detailed logs
- Check environment variable values are properly set
- Verify server is running on the correct port
- Test individual endpoints before running full workflows

## 📚 Additional Resources

- [Postman Documentation](https://learning.postman.com/)
- [Firecrawl API Docs](https://docs.firecrawl.dev/)
- [Apify API Reference](https://docs.apify.com/api/v2)
- [JigsawStack Documentation](https://docs.jigsawstack.com/)

## 🤝 Support

If you encounter issues:
1. Check the troubleshooting section above
2. Verify all environment variables are correctly set
3. Review server logs for detailed error messages
4. Test endpoints individually to isolate issues

Happy testing! 🎉