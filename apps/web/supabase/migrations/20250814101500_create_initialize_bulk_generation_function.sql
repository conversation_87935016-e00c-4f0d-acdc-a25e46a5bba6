-- Create function to initialize bulk generation tracking for a campaign
CREATE OR REPLACE FUNCTION public.initialize_bulk_generation(p_campaign_id uuid)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  UPDATE public.company_campaigns
  SET
    bulk_generation_status = 'pending',
    bulk_generation_progress = 0,
    bulk_generation_error = NULL,
    tasks_generated_count = COALESCE(tasks_generated_count, 0),
    tasks_content_generated_count = COALESCE(tasks_content_generated_count, 0),
    bulk_generation_started_at = EXTRACT(EPOCH FROM now())::bigint,
    bulk_generation_completed_at = NULL
  WHERE id = p_campaign_id;
END;
$$;

-- Optional overload accepting text to avoid implicit cast issues from drivers
CREATE OR REPLACE FUNCTION public.initialize_bulk_generation(p_campaign_id text)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  PERFORM public.initialize_bulk_generation((p_campaign_id)::uuid);
END;
$$;

-- Grants
GRANT EXECUTE ON FUNCTION public.initialize_bulk_generation(uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION public.initialize_bulk_generation(uuid) TO service_role;
GRANT EXECUTE ON FUNCTION public.initialize_bulk_generation(text) TO authenticated;
GRANT EXECUTE ON FUNCTION public.initialize_bulk_generation(text) TO service_role;

