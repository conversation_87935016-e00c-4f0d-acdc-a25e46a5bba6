import { useState } from 'react';
import { toast } from 'sonner';

interface UseAutoContentGenerationReturn {
  triggerContentGeneration: (campaignId: string, companyId: string) => Promise<boolean>;
  isTriggering: boolean;
  error: string | null;
  progress: number;
  resetProgress: () => void;
}

/**
 * Hook for automatically triggering content generation after task creation
 */
export function useAutoContentGeneration(): UseAutoContentGenerationReturn {
  const [isTriggering, setIsTriggering] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [progress, setProgress] = useState(0);

  const resetProgress = () => {
    setProgress(0);
    setError(null);
  };

  const triggerContentGeneration = async (campaignId: string, companyId: string): Promise<boolean> => {
    if (!campaignId || !companyId) {
      setError('Missing campaign ID or company ID');
      return false;
    }

    setIsTriggering(true);
    setError(null);
    setProgress(0);

    try {
      // Simulate progress for better UX
      setProgress(25);
      
      const response = await fetch('/api/ai/auto-trigger-content', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          campaign_id: campaignId,
          company_id: companyId,
        }),
      });

      setProgress(75);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP ${response.status}`);
      }

      const result = await response.json();
      setProgress(100);
      
      if (result.success) {
        toast.success('Content generation started automatically!', {
          description: 'Your content is being generated in the background. You can continue working while it processes.',
          duration: 5000,
        });
        return true;
      } else {
        throw new Error(result.error || 'Unknown error occurred');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to trigger content generation';
      setError(errorMessage);
      console.error('Auto content generation error:', err);
      
      // Show user-friendly error message
      toast.error('Content generation could not be started automatically', {
        description: 'You can trigger it manually later from the Content Studio.',
        duration: 5000,
      });
      return false;
    } finally {
      setIsTriggering(false);
      // Reset progress after a delay to show completion
      setTimeout(() => setProgress(0), 2000);
    }
  };

  return {
    triggerContentGeneration,
    isTriggering,
    error,
    progress,
    resetProgress,
  };
}
