'use client';

import { useState } from 'react';
import { TaskStatus } from '~/services/task-status';
import { useTaskStatuses } from '../hooks/use-task-statuses';
import { Check, Circle, Clock, Grip, PencilIcon, PlusIcon, Trash2Icon } from 'lucide-react';
import { Button } from '@kit/ui/button';
import { Input } from '@kit/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@kit/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@kit/ui/dialog';
import { Alert, AlertDescription } from '@kit/ui/alert';
import { Popover, PopoverContent, PopoverTrigger } from '@kit/ui/popover';
import { HexColorPicker } from 'react-colorful';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  useSortable,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { useQuery as useZeroQuery } from '@rocicorp/zero/react';
import { useZero } from '~/hooks/use-zero';
import { getUniqueId } from '~/services/utils';



// Icon options available
const ICON_OPTIONS = [
  { label: 'Circle', value: 'Circle', icon: Circle },
  { label: 'Clock', value: 'Clock', icon: Clock },
  { label: 'Check', value: 'Check', icon: Check },
];

interface StatusItemProps {
  status: TaskStatus;
  onEdit: (status: TaskStatus) => void;
  onDelete: (status: TaskStatus) => void;
  isDefaultStatus: boolean;
}

function SortableStatusItem({ status, onEdit, onDelete, isDefaultStatus }: StatusItemProps) {
  const { attributes, listeners, setNodeRef, transform, transition } = useSortable({
    id: status.id,
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  // Find icon component
  const IconComponent = ICON_OPTIONS.find(opt => opt.value === status.icon)?.icon || Circle;

  return (
    <div
      ref={setNodeRef}
      style={style}
      className="flex items-center justify-between p-3 border rounded-md mb-2 bg-card"
    >
      <div className="flex items-center gap-3">
        <div {...attributes} {...listeners} className="cursor-grab">
          <Grip className="h-5 w-5 text-muted-foreground" />
        </div>
        <div 
          className="flex items-center gap-2"
          style={{ color: status.color }}
        >
          <IconComponent className="h-4 w-4" />
          <span className="font-medium">{status.display_name}</span>
        </div>
      </div>
      
      <div className="flex items-center gap-1">
        <Button variant="ghost" size="icon" onClick={() => onEdit(status)}>
          <PencilIcon className="h-4 w-4" />
        </Button>
        {!isDefaultStatus && (
          <Button variant="ghost" size="icon" className="text-destructive" onClick={() => onDelete(status)}>
            <Trash2Icon className="h-4 w-4" />
          </Button>
        )}
      </div>
    </div>
  );
}

interface StatusFormProps {
  status?: TaskStatus;
  onSubmit: (data: {
    id?: string;
    name: string;
    displayName: string;
    color: string;
    icon: string;
  }) => void;
  onCancel: () => void;
  isEditing: boolean;
}

function StatusForm({ status, onSubmit, onCancel, isEditing }: StatusFormProps) {
  const [name, setName] = useState(status?.name || '');
  const [displayName, setDisplayName] = useState(status?.display_name || '');
  const [color, setColor] = useState(status?.color || '#3B82F6');
  const [icon, setIcon] = useState(status?.icon || 'Circle');
  const [error, setError] = useState('');


  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    // Simple validation
    if (!displayName.trim()) {
      setError('Display name is required');
      return;
    }


    onSubmit({
      id: status?.id,
      name: isEditing ? name.trim() : (displayName.toLowerCase().trim()).replace(/ /g, '-') + '-' + getUniqueId(),
      displayName: displayName.trim(),
      color,
      icon,
    });
  };

  // For existing statuses, name can't be changed (it's used as an identifier)
  // Check both uppercase and lowercase versions for default statuses
  const defaultStatusValues = ['draft', 'to do', 'in progress', 'done', 
                               'Draft', 'To Do', 'In Progress', 'Done'];
  const _isDefaultStatus = defaultStatusValues.includes(status?.name || '');

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="space-y-2">
        <label htmlFor="displayName" className="block text-sm font-medium">
          Display Name
        </label>
        <Input
          id="displayName"
          value={displayName}
          onChange={(e) => setDisplayName(e.target.value)}
          placeholder="e.g. In Review"
          required
        />
      </div>

      {/* {!isEditing && (
        <div className="space-y-2">
          <label htmlFor="name" className="block text-sm font-medium">
            Status ID
          </label>
          <Input
            id="name"
            value={name}
            onChange={(e) => setName(e.target.value)}
            placeholder="e.g. in-review (no spaces, lowercase)"
            required
          />
          <p className="text-xs text-muted-foreground">
            This will be used internally to track status. Use lowercase with no spaces.
          </p>
        </div>
      )} */}

      <div className="space-y-2">
        <label htmlFor="color" className="block text-sm font-medium">
          Color
        </label>
        <div className="flex items-center gap-2">
          <Popover>
            <PopoverTrigger asChild>
              <Button 
                variant="outline" 
                className="w-10 h-10 p-0 rounded-md" 
                style={{ backgroundColor: color }}
              />
            </PopoverTrigger>
            <PopoverContent className="w-auto p-3">
              <HexColorPicker color={color} onChange={setColor} />
            </PopoverContent>
          </Popover>
          <Input
            id="color"
            value={color}
            onChange={(e) => setColor(e.target.value)}
            className="w-24"
          />
        </div>
      </div>

      <div className="space-y-2">
        <label htmlFor="icon" className="block text-sm font-medium">
          Icon
        </label>
        <Select
          value={icon}
          onValueChange={setIcon}
        >
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {ICON_OPTIONS.map((option) => {
              const IconComp = option.icon;
              return (
                <SelectItem key={option.value} value={option.value}>
                  <div className="flex items-center gap-2">
                    <IconComp className="h-4 w-4" />
                    <span>{option.label}</span>
                  </div>
                </SelectItem>
              );
            })}
          </SelectContent>
        </Select>
      </div>

      <div className="flex justify-end gap-2 pt-2">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit">
          {isEditing ? 'Update' : 'Create'} Status
        </Button>
      </div>
    </form>
  );
}

interface TaskStatusManagerProps {
  accountId: string;
}

export function TaskStatusManager({ accountId }: TaskStatusManagerProps) {
  // const {
  //   statuses,
  //   isLoading,
  //   error,
  
  // } = useTaskStatuses(accountId);

  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [editingStatus, setEditingStatus] = useState<TaskStatus | null>(null);
  const [deleteConfirmStatus, setDeleteConfirmStatus] = useState<TaskStatus | null>(null);
  const zero = useZero();

  const[company_task_statuses, company_task_statuses_Result] = useZeroQuery(zero.query.company_task_statuses
    .where("company_id", "=", accountId)
    .orderBy('status_order', 'asc')
    , {
    ttl: '1d'
  });

  // DnD Kit setup
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Handle drag end
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    
    if (over && active.id !== over.id) {
      const oldIndex = company_task_statuses!.findIndex((s) => s.id === active.id);
      const newIndex = company_task_statuses!.findIndex((s) => s.id === over.id);
      
      const newOrder = arrayMove(company_task_statuses!, oldIndex, newIndex);
      
      // Update status_order for each status using Zero mutations (starting at 1)
      newOrder.forEach((status, index) => {
        zero.mutate.company_task_statuses.update({
          id: status.id,
          values: { 
            status_order: index + 1, // Start at 1, not 0
          },
        });
      });
    }
  };

  // Handle status creation
  const handleCreateStatus = (data: { name: string; displayName: string; color: string; icon: string }) => {
    const normalizedName = data.name.toLowerCase().trim();

    // Prevent duplicates client-side (case-insensitive)
    const exists = (company_task_statuses || []).some(s => s.name?.toLowerCase().trim() === normalizedName);
    if (exists) {
      setIsAddDialogOpen(false);
      return;
    }

    zero.mutate.company_task_statuses.insert({
      id: crypto.randomUUID(),
      values: {
        company_id: accountId,
        name: normalizedName,
        display_name: data.displayName,
        color: data.color,
        icon: data.icon,
        status_order: company_task_statuses?.length ? company_task_statuses.length + 1 : 1,
      },
    });
    setIsAddDialogOpen(false);
  };

  // Handle status update
  const handleUpdateStatus = (data: { id?: string; displayName: string; color: string; icon: string }) => {
    if (data.id) {
      zero.mutate.company_task_statuses.update({
        id: data.id,
        values: { 
          display_name: data.displayName,
          color: data.color,
          icon: data.icon,
        },
      });
    }
    setEditingStatus(null);
  };

  // Handle status deletion
  const handleDeleteConfirm = () => {
    if (deleteConfirmStatus) {
      zero.mutate.company_task_statuses.delete({
        id: deleteConfirmStatus.id,
      });
      setDeleteConfirmStatus(null);
    }
  };

  if (company_task_statuses_Result.type !== 'complete') {
    return <div className="py-4">Loading status configurations...</div>;
  }

  // if (company_task_statuses_Result.type == 'unknown') {
  //   return <div className="text-destructive py-4">Error loading statuses: {error.message}</div>;
  // }

  // Default statuses that cannot be deleted (but can be edited)
  const DEFAULT_STATUSES = ['draft', 'to do', 'in progress', 'done', 
                           'Draft', 'To Do', 'In Progress', 'Done'];

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">Task Statuses</h3>
        
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button size="sm">
              <PlusIcon className="h-4 w-4 mr-1" />
              Add Status
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create New Status</DialogTitle>
            </DialogHeader>
            <StatusForm
              onSubmit={handleCreateStatus}
              onCancel={() => setIsAddDialogOpen(false)}
              isEditing={false}
            />
          </DialogContent>
        </Dialog>
      </div>

      <div className="bg-muted p-4 rounded-md">
        <p className="text-sm text-muted-foreground mb-4">
          Drag and drop statuses to reorder columns in the Kanban board. You can add new statuses 
          or edit existing ones, but default statuses cannot be deleted.
        </p>

        <DndContext
          sensors={sensors}
          collisionDetection={closestCenter}
          onDragEnd={handleDragEnd}
        >
          <SortableContext
            items={company_task_statuses?.map(s => s.id) || []}
            strategy={verticalListSortingStrategy}
          >
            <div className="space-y-2">
              {company_task_statuses?.map((status) => (
                <SortableStatusItem
                  key={status.id}
                  status={status as TaskStatus}
                  onEdit={(s) => setEditingStatus(s)}
                  onDelete={(s) => setDeleteConfirmStatus(s)}
                  isDefaultStatus={DEFAULT_STATUSES.includes(status.name)}
                />
              ))}
            </div>
          </SortableContext>
        </DndContext>
      </div>

      {/* Edit Status Dialog */}
      <Dialog open={!!editingStatus} onOpenChange={(open) => !open && setEditingStatus(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Status</DialogTitle>
          </DialogHeader>
          {editingStatus && (
            <StatusForm
              status={editingStatus}
              onSubmit={handleUpdateStatus}
              onCancel={() => setEditingStatus(null)}
              isEditing={true}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={!!deleteConfirmStatus} onOpenChange={(open) => !open && setDeleteConfirmStatus(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Status</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <p>
              Are you sure you want to delete the status &quot;{deleteConfirmStatus?.display_name}&quot;? 
              This will move all tasks with this status to &quot;Draft&quot;.
            </p>
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setDeleteConfirmStatus(null)}>
                Cancel
              </Button>
              <Button variant="destructive" onClick={handleDeleteConfirm}>
                Delete
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}