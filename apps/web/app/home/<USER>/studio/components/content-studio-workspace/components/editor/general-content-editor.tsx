'use client';
import { Block } from "@blocknote/core";
import "@blocknote/core/fonts/inter.css";
import { BlockNoteView } from "@blocknote/mantine";
import "@blocknote/mantine/style.css";
import { useEffect, useState, useCallback } from "react";
import { updateCompanyContent } from "~/services/company-content";
import { useQuery as useZeroQuery } from '@rocicorp/zero/react';
import { createBlogPostBlocks, createInitialContent, createVideoBlock } from "~/utils/editor-util";
import { useBaseContent, useEditorContent, useImageContent, useVideoContent } from "../../context/ContentStudioContext";
import { useTheme } from "next-themes";
import { en as aiEn } from "@blocknote/xl-ai/locales";
import "@blocknote/xl-ai/style.css"; // add the AI stylesheet
import { createOpenAI } from '@ai-sdk/openai';
import { en } from "@blocknote/core/locales";
import {
  FormattingToolbar,
  FormattingToolbarController,
  getFormattingToolbarItems,
  useCreateBlockNote,
} from "@blocknote/react";
import {
  AIMenuController,
  AIToolbarButton,
  createAIExtension,
  createBlockNoteAIClient,
} from "@blocknote/xl-ai";
import { useZero } from "~/hooks/use-zero";
import { CompanyContent } from "~/types/company-content";
import { z } from "zod";
import { Textarea } from "@kit/ui/textarea";
import { Input } from "@kit/ui/input";
import { defaultContent } from "./default-content";
import { debounce } from "lodash";
export default function GeneralContentEditor({ 
  companyContent,
  editable = true
}: { 
  companyContent: CompanyContent,
  editable?: boolean
}) {
  // Track if this is the initial content load
  console.log("companyContent", companyContent);
  const { theme } = useTheme();
  const zero = useZero();  
  const [isLoadingGeneratedContent, setIsLoadingGeneratedContent] = useState(false);


  const client = createBlockNoteAIClient({
    apiKey: 'my-secret-token',
    baseURL: '/api/ai/stream-content',
  });

  const model = createOpenAI({
    ...client.getProviderSettings("google")
  })("gemini-2.5-pro")

  const editor = useCreateBlockNote({
    // Register the AI extension
    dictionary: {
      ...en,
      ai: aiEn, // add default translations for the AI extension
    },
    extensions: [
      createAIExtension({
        model
      }),
    ],
  });

  const { setEditor } = useEditorContent();

  // Create debounced update function (saves every 5 seconds)
  const debouncedUpdateContent = useCallback(
    debounce((blocks: Block[]) => {
      zero.mutate.company_content.update({
        id: companyContent.id,
        values: {
          content_editor_template: blocks,
        }
      });
    }, 3000),
    [companyContent.id, zero]
  );

  // Handle initial content setup
  useEffect(() => {
    if (!editor) return;
    setEditor(editor);

    async function updateContent() {
      let blocks;
      
      console.log('Content loading priority check:', {
        hasEditorTemplate: !!companyContent.content_editor_template,
        editorTemplateLength: companyContent.content_editor_template?.length || 0,
        hasGeneratedContent: !!companyContent.content,
        contentLength: companyContent.content?.length || 0,
        contentGenerationStatus: companyContent.content_generation_status,
        contentId: companyContent.id
      });
      
      // First priority: Check for existing editor template (user-edited content)
      if (companyContent.content_editor_template && companyContent.content_editor_template.length > 0) {
        blocks = companyContent.content_editor_template;
        console.log('✅ Loading existing editor template content');
      }
      // Second priority: Check for generated content from AI
      else if (companyContent.content && companyContent.content.trim() !== '') {
        try {
          setIsLoadingGeneratedContent(true);
          console.log('🔄 Converting generated content to blocks. Content preview:', companyContent.content.substring(0, 100) + '...');
          
          let contentToParse = companyContent.content;
          
          // Handle different content formats
          if (companyContent.content.includes('<') && companyContent.content.includes('>')) {
            // Content appears to be HTML, try to extract text content
            console.log('📝 Content appears to be HTML, extracting text content');
            // Simple HTML tag removal for now - could be enhanced with proper HTML parsing
            contentToParse = companyContent.content.replace(/<[^>]*>/g, '');
          }
          
          // Convert the generated text content to BlockNote blocks
          blocks = await editor.tryParseMarkdownToBlocks(contentToParse);
          console.log('✅ Successfully converted content to blocks:', blocks);
          
          // Save the converted blocks to content_editor_template for future use
          await zero.mutate.company_content.update({
            id: companyContent.id,
            values: {
              content_editor_template: blocks,
            }
          });
          console.log('💾 Saved converted blocks to content_editor_template');
        } catch (error) {
          console.error('❌ Failed to parse generated content:', error);
          console.log('Raw content that failed to parse:', companyContent.content);
          
          // Try to create a simple paragraph block as fallback
          try {
            blocks = [{
              id: crypto.randomUUID(),
              type: "paragraph",
              props: {
                textColor: "default",
                backgroundColor: "default",
                textAlignment: "left"
              },
              content: [{
                type: "text",
                text: companyContent.content,
                styles: {}
              }],
              children: []
            }];
            console.log('🔄 Created fallback paragraph block from raw content');
          } catch (fallbackError) {
            console.error('❌ Even fallback block creation failed:', fallbackError);
            blocks = defaultContent;
          }
        } finally {
          setIsLoadingGeneratedContent(false);
        }
      }
      // Last resort: Use default content
      else {
        blocks = defaultContent;
        console.log('📝 Using default content - no existing or generated content found');
      }
      
      console.log('Final blocks to load:', blocks);
      editor.replaceBlocks(editor.document, blocks);
    }

    updateContent();
  }, [companyContent.content_editor_template, companyContent.content, companyContent.id, companyContent.content_generation_status, editor, setEditor, zero]);

  // Poll for content generation status changes
  useEffect(() => {
    if (!companyContent.id || companyContent.content_generation_status === 'completed') return;

    const pollInterval = setInterval(async () => {
      try {
        // Check if content has been generated by looking at the current companyContent
        // Since we're using Zero, the data should automatically sync
        if (companyContent.content && companyContent.content.trim() !== '' && 
            companyContent.content_generation_status === 'completed') {
          console.log('🔄 Content generation completed, content should be available');
          // The content loading effect above should handle this automatically
          // when the dependencies change
        }
      } catch (error) {
        console.error('Error in content polling:', error);
      }
    }, 5000); // Poll every 5 seconds

    return () => clearInterval(pollInterval);
  }, [companyContent.id, companyContent.content_generation_status, companyContent.content]);

  // Cleanup debounced function on unmount
  useEffect(() => {
    return () => {
      debouncedUpdateContent.cancel();
    };
  }, [debouncedUpdateContent]);

  return (
    <>
      {isLoadingGeneratedContent && (
        <div className="absolute inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
            <p className="text-sm text-muted-foreground">Converting generated content...</p>
          </div>
        </div>
      )}
      <BlockNoteView
        editor={editor} 
        editable={editable}
        theme={theme as "light" | "dark"} 
        formattingToolbar={false}
        onChange={() => {
          const blocks = editor.document;
          debouncedUpdateContent(blocks);
        }}
      >
         <AIMenuController />
         <FormattingToolbarWithAI />
      </BlockNoteView>
    </>
  );
}

function FormattingToolbarWithAI() {
  return (
    <FormattingToolbarController
      formattingToolbar={() => (
        <FormattingToolbar>
          {...getFormattingToolbarItems()}
          {/* Add the AI button */}
          <AIToolbarButton />
        </FormattingToolbar>
      )}
    />
  );
}
 