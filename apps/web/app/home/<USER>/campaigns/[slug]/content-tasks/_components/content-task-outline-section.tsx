'use client';

import { useState, useEffect } from 'react';
import { Separator } from '@kit/ui/separator';
import { CampaignIdea } from '~/types/campaign-idea';
import { CompanyContent } from '~/types/company-content';
import { Campaign } from '~/types/Campaign';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { toast } from 'sonner';
import { upsertCampaignIdea as _upsertCampaignIdea, upsertCampaignIdea } from '~/services/campaign';
import { saveContentTasks as _saveContentTasks, saveContentTasks, deleteCompanyContent } from '~/services/company-content';
import { MarketingContentProps } from '../_types/brief-types';
import IdeaSelector from './idea-selector';
import ContentTasksDisplay from './content-tasks-display';
import { Button } from '@kit/ui/button';
import { useBrandData } from '~/hooks/use-brand-data';
import { extractBrandBrief, extractCampaignBrief } from '~/utils/brief.util';
import { useCampaignContext } from '../../_components/campaign-context';
import { useAutoContentGeneration } from '~/hooks/use-auto-content-generation';
import { Spinner } from '@kit/ui/spinner';
import { Skeleton } from '@kit/ui/skeleton';
import { Progress } from '@kit/ui/progress';
import { Alert, AlertDescription } from '@kit/ui/alert';
import { AlertCircle } from 'lucide-react';

interface CreativeBriefSectionProps {
  campaign: Campaign;
  campaignIdeas: CampaignIdea[];
  companyContent: CompanyContent[];
}

export default function ContentTaskOutlineSection({
  campaign,
  campaignIdeas,
  companyContent,
}: CreativeBriefSectionProps) {
  const workspace = useTeamAccountWorkspace();
  const brand = useBrandData(workspace.account.id);
  const { triggerContentGeneration, isTriggering, error, progress } = useAutoContentGeneration();
  
  // Safely get the first idea if available
  const firstIdea = campaignIdeas && campaignIdeas.length > 0 ? campaignIdeas[0] : undefined;
  
  // State management
  const [selectedIdea, setSelectedIdea] = useState<CampaignIdea | undefined>(firstIdea);
  const [brief, setBrief] = useState<MarketingContentProps | Record<string, never>>(
    (firstIdea?.brief as MarketingContentProps) || {}
  );

  const [briefLoading, setBriefLoading] = useState(false);
  console.log({briefLoading});
  //filter company content by selectd idea id as the initial content tasks state
  const [contentTasks, _setContentTasks] = useState<CompanyContent[]>(companyContent.filter((content) => content.idea_id === selectedIdea?.id));
  const [tasksLoading, setTasksLoading] = useState(false);
  const [activeContentType, setActiveContentType] = useState<string>('all');
  const [selectedContentTypes, setSelectedContentTypes] = useState<string[]>(
    Array.isArray(firstIdea?.content_types) ? firstIdea.content_types as string[] : []
  );
  
  useEffect(() => {
    if(contentTasks.length > 0) {
        //set the initial content types
        //ensure we only get the unique content types
        setSelectedContentTypes(Array.from(new Set(contentTasks.map((content) => content.content_type))));
    }
  }, [selectedIdea, contentTasks]);

  // Generate brief on page load if no brief exists
  useEffect(() => {
    if (selectedIdea && (!selectedIdea.brief || !brief || Object.keys(selectedIdea.brief || {}).length === 0)) {
      generateBrief();
    } else if (selectedIdea?.brief) {
      setBrief(selectedIdea.brief as MarketingContentProps);
      _setContentTasks(companyContent.filter((content) => content.idea_id === selectedIdea?.id));
    }
  }, [selectedIdea]);

  const generateBrief = async () => {
    if (!selectedIdea) {
      toast.error('No idea selected');
      return;
    }
    
    try {
      setBriefLoading(true);
      
      // Make sure we have at least the idea content available
      if (!selectedIdea.content || selectedIdea.content.trim() === '') {
        toast.error('The selected idea has no content');
        setBriefLoading(false);
        return;
      }
      
      // Explicitly set 'en' as default language if array is empty
      const languages = (Array.isArray(selectedIdea.languages) && selectedIdea.languages.length > 0)
        ? selectedIdea.languages
        : ['en'];
      
      // Set defaults for API fields to prevent validation errors
      const brandName = workspace.account.name || 'Company Name';
      console.log({brand});
      const brandBrief = brand.data ? extractBrandBrief(brand.data) : 'Brief Not Provided';
      const campaignBrief = extractCampaignBrief(campaign) || 'No objective provided';
      
      const response = await fetch('/api/ai/brief', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          brand_name: brandName,
          brand_brief: brandBrief,
          campaign_brief: campaignBrief,
          //@ts-expect-error maps should work TODO: fix this
          product_info: campaign.documents ? campaign.documents?.map((doc: any) => `${doc.title}: ${doc.content}`).join('\n\n') : "No Product Info Provided",
          idea: selectedIdea.content,
          languages, // Always set, defaults to 'en'
          supported_content_types: selectedIdea.content_types,
          supported_channels: selectedIdea.channels,
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to generate brief');
      }
 
      const data = await response.json();
      // console.log("GENERATED BRIEF", {data});
      setBrief(data.creative_brief);
      
      if (selectedIdea?.id) {
        await upsertCampaignIdea({
          id: selectedIdea.id,
          brief: data.creative_brief,
        });
      }
    
    } catch (error) {
      console.error('Error generating brief:', error);
      toast.error('Failed to generate brief');
    } finally {
      setBriefLoading(false);
    }
  };

  const generateContentTasks = async () => {
    if (!selectedIdea?.id) {
      toast.error('No idea selected');
      return;
    }
    
    try {
      setTasksLoading(true);
      
      // Always use a valid language
      const language = (Array.isArray(selectedIdea.languages) && selectedIdea.languages.length > 0)
        ? selectedIdea.languages[0] 
        : 'en';
      
      const response = await fetch('/api/ai/generate-tasks', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          creativeBrief: brief,
          language,
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to generate tasks');
      }
      
      const data = await response.json();
      const tasks = await saveContentTasks(
        data, 
        campaign.id, 
        selectedIdea.id, 
        campaign.company_id
      );
      _setContentTasks(tasks);

      // Auto-trigger content generation after tasks are created
      const contentGenSuccess = await triggerContentGeneration(campaign.id, campaign.company_id);
      
      if (contentGenSuccess) {
        toast.success('Tasks created and content generation started automatically!');
      } else {
        toast.success('Tasks created successfully! Content generation will need to be triggered manually.');
      }
    } catch (error) {
      console.error('Error generating tasks:', error);
      toast.error('Failed to generate tasks');
    } finally {
      setTasksLoading(false);
    }
  };
  
  const handleContentTypeChange = async (contentTypeCode: string) => {
    setActiveContentType(contentTypeCode);
  };
  
  const handleIdeaSelect = (idea: CampaignIdea) => {
    setSelectedIdea(idea);
    
    // Reset content types based on selected idea
    setSelectedContentTypes(
      Array.isArray(idea?.content_types) ? idea.content_types as string[] : []
    );
  };

  // Make content tasks and campaign ideas available via context for the bottom nav
  const { 
    setContentTasks: setContextContentTasks,
    setCampaignIdeas: setContextCampaignIdeas,
    setSelectedIdeaIds
  } = useCampaignContext();
  
  useEffect(() => {
    setContextContentTasks(contentTasks);
  }, [contentTasks, setContextContentTasks]);
  
  // Store campaign ideas in context
  useEffect(() => {
    setContextCampaignIdeas(campaignIdeas);
    // Initialize selected ideas with the current selected idea
    if (selectedIdea?.id) {
      setSelectedIdeaIds([selectedIdea.id]);
    }
  }, [campaignIdeas, selectedIdea, setContextCampaignIdeas, setSelectedIdeaIds]);

  const handleDeleteTask = async (id: string) => {
    try {
      await deleteCompanyContent(id);
      _setContentTasks(prevTasks => prevTasks.filter(task => task.id !== id));
      toast.success('Task deleted successfully');
    } catch (error) {
      console.error('Error deleting task:', error);
      toast.error('Failed to delete task');
    }
  };
  
  // Early return for loading state
  if (tasksLoading || isTriggering) {
    return (
      <div className="space-y-6">
        <h2 className="text-2xl font-semibold tracking-tight">Content Tasks</h2>
        <div className="relative">
          <div className="absolute inset-0 flex flex-col items-center justify-center z-10">
            <Spinner className="w-8 h-8 mb-2" />
            <p className="text-muted-foreground">
              {tasksLoading ? 'Tasks are being generated...' : 'Content generation is being triggered...'}
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 opacity-50">
            {[1, 2, 3].map((i) => (
              <Skeleton key={i} className="h-[200px] w-full" />
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-semibold tracking-tight">Creative Brief</h2>
          <div className="flex gap-2">
            <Button 
              onClick={generateBrief} 
              disabled={briefLoading}
              variant="outline"
            >
              {briefLoading ? 'Generating...' : 'Generate Brief'}
            </Button>
            <Button 
              onClick={generateContentTasks} 
              disabled={tasksLoading || !brief || Object.keys(brief).length === 0}
            >
              {tasksLoading ? 'Generating Tasks...' : 'Generate Content Tasks'}
            </Button>
          </div>
        </div>

        {/* Show manual content generation trigger if we have tasks but content generation failed */}
        {contentTasks.length > 0 && (
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 bg-muted rounded-lg">
              <div>
                <h3 className="font-medium">Content Tasks Generated</h3>
                <p className="text-sm text-muted-foreground">
                  {contentTasks.length} tasks created. You can now trigger content generation manually if needed.
                </p>
              </div>
              <Button 
                onClick={() => triggerContentGeneration(campaign.id, campaign.company_id)}
                disabled={isTriggering}
                variant="secondary"
                size="sm"
              >
                {isTriggering ? 'Triggering...' : 'Trigger Content Generation'}
              </Button>
            </div>

            {/* Show progress when triggering content generation */}
            {isTriggering && progress > 0 && (
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Triggering content generation...</span>
                  <span>{progress}%</span>
                </div>
                <Progress value={progress} className="w-full" />
              </div>
            )}

            {/* Show error if content generation failed */}
            {error && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  Failed to trigger content generation: {error}. You can try again or trigger it manually later.
                </AlertDescription>
              </Alert>
            )}
          </div>
        )}

        <BriefDisplay 
          brief={brief} 
          isLoading={briefLoading}
          campaignIdeaId={selectedIdea?.id}
        />
      </div>

      <Separator />

      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-semibold tracking-tight">Content Tasks</h2>
          <IdeaSelector 
            ideas={campaignIdeas}
            selectedIdea={selectedIdea}
            onIdeaSelect={handleIdeaSelect}
          />
        </div>

        <ContentTasksDisplay
          contentTasks={contentTasks}
          contentTypes={selectedContentTypes}
          activeContentType={activeContentType}
          onContentTypeChange={handleContentTypeChange}
          isLoading={tasksLoading}
          onDelete={handleDeleteTask}
        />
      </div>
    </div>
  );
} 