'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { Button } from '@kit/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@kit/ui/dialog';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@kit/ui/form';
import { Input } from '@kit/ui/input';
import { Plus } from 'lucide-react';
import { useState } from 'react';
import { useZeroInstance } from '~/app/hooks/use-zero';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { useRouter } from 'next/navigation';
import { Calendar } from '@kit/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@kit/ui/popover';
import { CalendarIcon } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@kit/ui/utils';

const CreateCampaignSchema = z.object({
  name: z.string().min(1, 'Campaign name is required'),
  objective: z.string().min(1, 'Campaign objective is required'),
  start_date: z.date().optional(),
  end_date: z.date().optional(),
});

type CreateCampaignFormData = z.infer<typeof CreateCampaignSchema>;

export default function CreateCampaignDialog({ isNew }: { isNew: boolean }) {
  const [open, setOpen] = useState(isNew);
  const workspace = useTeamAccountWorkspace();
  const router = useRouter();
  const zero = useZeroInstance();
  const form = useForm<CreateCampaignFormData>({
    resolver: zodResolver(CreateCampaignSchema),
    defaultValues: {
      name: '',
      objective: '',
      start_date: undefined,
      end_date: undefined,
    },
  });

  const onSubmit = async (data: CreateCampaignFormData) => {
    console.log('Campaign data:', data);
    try {
      const id = crypto.randomUUID();
      const start = data.start_date ? data.start_date.toISOString() : new Date().toISOString();
      const end = data.end_date ? data.end_date.toISOString() : new Date(Date.now() + 1000*60*60*24*7).toISOString();

      zero.mutate.company_campaigns.insert({
        id,
        company_id: workspace.account.id,
        user_id: workspace.user.id!,
        name: data.name,
        objective: data.objective,
        start_date: start,
        end_date: end,
        templateId: '',
        external_research: [],
        products: [],
        target_icps: [],
        target_personas: [],
      });

      // Optionally set this as selected campaign
      zero.mutate.user_cache.upsert({
        user_id: workspace.user.id!,
        values: { selected_campaign: id }
      });

      // Navigate to tasks where users can see generation progress
      router.push(`/home/<USER>/tasks`);
      setOpen(false);
      form.reset();
    } catch (e) {
      console.error('Failed to create campaign via Zero:', e);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button
          className="fixed bottom-8 right-8 h-14 w-14 rounded-full shadow-lg"
        >
          <Plus className="h-6 w-6" />
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Create New Campaign</DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Campaign Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter campaign name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="objective"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Campaign Goal</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter a brief goal for the campaign" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
              <FormField
                control={form.control}
                name="start_date"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Start Date</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              "w-[240px] pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value ? (
                              format(field.value, "PPP")
                            ) : (
                              <span>Pick a date</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) =>
                            date < new Date()
                          }
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="end_date"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>End Date</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              "w-[240px] pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value ? (
                              format(field.value, "PPP")
                            ) : (
                              <span>Pick a date</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) =>
                            date < (form.getValues("start_date") || new Date())
                          }
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />
            <Button type="submit" className="w-full">
              Create Campaign
            </Button>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
} 