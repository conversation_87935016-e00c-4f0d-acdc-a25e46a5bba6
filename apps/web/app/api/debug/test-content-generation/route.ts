import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

export async function POST(req: NextRequest) {
  try {
    const { campaign_id } = await req.json();
    
    if (!campaign_id) {
      return NextResponse.json({ error: 'campaign_id required' }, { status: 400 });
    }

    // Get campaign and company info
    const url = process.env.NEXT_PUBLIC_SUPABASE_URL!;
    const serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
    const supabase = createClient(url, serviceKey, { auth: { persistSession: false } });

    const { data: campaign, error: campaignError } = await supabase
      .from('company_campaigns')
      .select('id, company_id, name')
      .eq('id', campaign_id)
      .single();

    if (campaignError || !campaign) {
      return NextResponse.json({ error: 'Campaign not found' }, { status: 404 });
    }

    // Trigger bulk generation
    const triggerRes = await fetch(`${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/api/content-generation/trigger`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${serviceKey}`
      },
      body: JSON.stringify({
        action: 'bulk_campaign_generation',
        campaign_id: campaign.id,
        company_id: campaign.company_id,
      })
    });

    const triggerResult = await triggerRes.json();

    return NextResponse.json({
      success: true,
      campaign: campaign.name,
      trigger_status: triggerRes.status,
      trigger_result: triggerResult,
      message: 'Content generation triggered. Check the campaign and tasks for updates.'
    });

  } catch (error: any) {
    console.error('Test content generation error:', error);
    return NextResponse.json({ 
      error: 'Internal server error', 
      details: error?.message || error 
    }, { status: 500 });
  }
}

export async function GET() {
  try {
    const url = process.env.NEXT_PUBLIC_SUPABASE_URL!;
    const serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
    const supabase = createClient(url, serviceKey, { auth: { persistSession: false } });

    // Get recent campaigns with their task counts
    const { data: campaigns, error } = await supabase
      .from('company_campaigns')
      .select(`
        id, 
        name, 
        bulk_generation_status, 
        bulk_generation_progress,
        tasks_generated_count,
        tasks_content_generated_count,
        created_at
      `)
      .order('created_at', { ascending: false })
      .limit(10);

    if (error) throw error;

    return NextResponse.json({
      campaigns: campaigns || [],
      instructions: 'POST to this endpoint with {"campaign_id": "uuid"} to trigger content generation'
    });

  } catch (error: any) {
    return NextResponse.json({ 
      error: 'Failed to fetch campaigns', 
      details: error?.message || error 
    }, { status: 500 });
  }
}
