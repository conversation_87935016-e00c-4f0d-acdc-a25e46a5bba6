import { NextRequest, NextResponse } from 'next/server';
import OpenAI from 'openai';

export async function POST(req: NextRequest) {
  try {
    const { prompt } = await req.json();
    
    const openaiKey = process.env.OPENAI_API_KEY;
    if (!openaiKey) {
      return NextResponse.json({ error: 'OpenAI API key not configured' }, { status: 500 });
    }

    const openai = new OpenAI({ apiKey: openaiKey });
    
    const completion = await openai.chat.completions.create({
      model: 'gpt-4o-mini',
      messages: [
        {
          role: 'user',
          content: prompt || 'Write a short LinkedIn post about a new API launch.'
        }
      ],
      temperature: 0.7,
      max_tokens: 200
    });

    const content = completion.choices[0]?.message?.content || 'No content generated';

    return NextResponse.json({
      success: true,
      content,
      model: 'gpt-4o-mini',
      usage: completion.usage
    });

  } catch (error: any) {
    console.error('Simple content test error:', error);
    return NextResponse.json({ 
      error: 'Content generation failed', 
      details: error?.message || error 
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'POST with {"prompt": "your prompt here"} to test content generation'
  });
}
