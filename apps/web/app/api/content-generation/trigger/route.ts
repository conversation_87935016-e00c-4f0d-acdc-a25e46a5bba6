import { NextRequest, NextResponse } from 'next/server';

// Minimal proxy to forward bulk content generation requests from the Zero server mutator
// to the push server (or any backend worker) responsible for generating content.
// This keeps the existing call site in companyCampaignsInsert working:
//   fetch(`${SITE_URL}/api/content-generation/trigger`, { Authorization: Bearer SRK, body: { action:'bulk_campaign_generation', ... }})

export async function POST(req: NextRequest) {
  try {
    const authHeader = req.headers.get('authorization') || '';
    const serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    const pushServer = process.env.NEXT_PUBLIC_PUSH_SERVER || process.env.NEXT_PUBLIC_BACKEND_URL;

    if (!pushServer) {
      return NextResponse.json({ error: 'Push server URL not configured' }, { status: 500 });
    }

    if (!serviceKey) {
      return NextResponse.json({ error: 'Service key not configured' }, { status: 500 });
    }

    // Only allow calls from server with the service role key
    if (!authHeader.includes(serviceKey)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await req.json();

    // Forward to push server route; keep same payload
    const res = await fetch(`${pushServer}/content-generation/trigger`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(body),
    });

    const text = await res.text();
    return new NextResponse(text, { status: res.status, headers: { 'Content-Type': 'application/json' } });
  } catch (error) {
    console.error('Error in content-generation trigger proxy:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

