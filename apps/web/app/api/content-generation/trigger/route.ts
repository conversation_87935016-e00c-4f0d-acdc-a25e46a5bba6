import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import OpenAI from 'openai';

// Proxy + fallback bulk content generator.
// 1) Try forwarding to push server
// 2) If upstream missing or fails, run a minimal local bulk generator using existing APIs

function generateContentPrompt(task: any): string {
  const { content_type, channel, task_title, task_description } = task;

  return `Create ${content_type} content for ${channel} with the following details:

Title: ${task_title}
Description: ${task_description}

Requirements:
- Write engaging, professional content appropriate for ${channel}
- Match the tone and style expected for ${content_type}
- Include relevant hashtags if appropriate for the platform
- Keep it concise and actionable
- Make it compelling and shareable

Generate only the final content without any explanations or markdown formatting.`;
}

async function generateContentWithOpenAI(prompt: string): Promise<string> {
  const openaiKey = process.env.OPENAI_API_KEY;

  // If no OpenAI key, use mock content for development
  if (!openaiKey) {
    console.log('No OpenAI key found, generating mock content for:', prompt.substring(0, 100));
    return generateMockContent(prompt);
  }

  try {
    const openai = new OpenAI({ apiKey: openaiKey });

    const completion = await openai.chat.completions.create({
      model: 'gpt-4o-mini',
      messages: [
        {
          role: 'system',
          content: 'You are a professional content creator. Generate high-quality, engaging content based on the user\'s requirements. Return only the content without any markdown formatting or extra explanations.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.7,
      max_tokens: 1000
    });

    return completion.choices[0]?.message?.content || 'Content generation failed';
  } catch (error) {
    console.error('OpenAI API error, falling back to mock content:', error);
    return generateMockContent(prompt);
  }
}

function generateMockContent(prompt: string): string {
  // Extract content type and channel from prompt for better mock content
  const contentType = prompt.match(/Create (\w+)/i)?.[1] || 'content';
  const channel = prompt.match(/for (\w+)/i)?.[1] || 'social media';

  const mockTemplates = {
    'LinkedIn': `🚀 Exciting news! We're launching our new developer API that will revolutionize how you build applications.

Key features:
✅ Lightning-fast response times
✅ Comprehensive documentation
✅ 99.9% uptime guarantee
✅ Developer-friendly SDKs

Ready to transform your development workflow? Check out our API docs and start building today!

#API #Developers #Innovation #TechLaunch`,

    'Twitter': `🚀 NEW: Developer API is live!

⚡ Fast, reliable, and easy to integrate
📚 Complete docs + SDKs ready
🔧 Built by developers, for developers

Start building: [link]

#API #DevTools #Launch`,

    'Blog': `# Introducing Our New Developer API

We're excited to announce the launch of our comprehensive developer API, designed to empower developers with powerful tools and seamless integration capabilities.

## What's New

Our API offers:
- **High Performance**: Sub-100ms response times
- **Comprehensive Coverage**: Full feature parity with our platform
- **Developer Experience**: Intuitive design with extensive documentation
- **Reliability**: 99.9% uptime SLA

## Getting Started

Ready to dive in? Visit our developer portal to:
1. Create your API account
2. Generate your first API key
3. Explore our interactive documentation
4. Download SDKs for your preferred language

## What's Next

This is just the beginning. We're committed to continuously improving our API based on your feedback and needs.

Start building today and join our growing community of developers!`,

    'Email': `Subject: 🚀 Your Developer API is Ready!

Hi Developer,

Great news! Our new API is now live and ready for integration.

What you get:
• Fast, reliable endpoints
• Comprehensive documentation
• Multiple SDK options
• 24/7 developer support

Get started in minutes:
1. Visit our developer portal
2. Generate your API key
3. Start building

Questions? Our developer team is here to help.

Happy coding!
The API Team`
  };

  // Return appropriate mock content based on channel
  if (channel.toLowerCase().includes('linkedin')) return mockTemplates.LinkedIn;
  if (channel.toLowerCase().includes('twitter') || channel.toLowerCase().includes('x')) return mockTemplates.Twitter;
  if (channel.toLowerCase().includes('blog') || contentType.toLowerCase().includes('blog')) return mockTemplates.Blog;
  if (channel.toLowerCase().includes('email')) return mockTemplates.Email;

  // Default social media content
  return `🚀 Exciting announcement! We're launching something amazing that will change how you work.

✨ Key highlights:
• Innovative features
• Easy to use
• Built for professionals
• Available now

Ready to get started? Learn more and join thousands of satisfied users!

#Innovation #Launch #NewFeatures`;
}

async function runLocalBulkGeneration(params: { campaign_id: string; company_id: string }) {
  const { campaign_id } = params;
  const url = process.env.NEXT_PUBLIC_SUPABASE_URL!;
  const serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
  const supabase = createClient(url, serviceKey, { auth: { persistSession: false } });

  // Mark campaign as in_progress
  await supabase.from('company_campaigns').update({ bulk_generation_status: 'in_progress' }).eq('id', campaign_id);

  // Fetch pending tasks (status pending or null)
  const { data: tasks, error: fetchErr } = await supabase
    .from('company_content')
    .select('id, content_type, channel, language, task_title, task_description')
    .eq('campaign_id', campaign_id)
    .or('content_generation_status.is.null,content_generation_status.eq.pending')
    .limit(1000);

  if (fetchErr) throw fetchErr;
  if (!tasks || tasks.length === 0) return { generated: 0 };

  let generated = 0;

  for (const t of tasks) {
    // Set status to generating
    await supabase.from('company_content').update({ content_generation_status: 'generating', is_generating: true }).eq('id', t.id);

    try {
      // Generate content using OpenAI directly (fallback if streaming API not available)
      const prompt = generateContentPrompt(t);
      const generatedContent = await generateContentWithOpenAI(prompt);

      await supabase
        .from('company_content')
        .update({
          content: generatedContent,
          content_generation_status: 'completed',
          is_generating: false,
          content_generation_attempts: 1,
        })
        .eq('id', t.id);

      generated += 1;
    } catch (e: any) {
      console.error(`Failed to generate content for task ${t.id}:`, e);
      await supabase
        .from('company_content')
        .update({
          content_generation_status: 'failed',
          content_generation_error: String(e?.message || e),
          is_generating: false,
          content_generation_attempts: 1,
        })
        .eq('id', t.id);
    }
  }

  // Update campaign counters
  await supabase
    .from('company_campaigns')
    .update({
      bulk_generation_status: 'completed',
      tasks_content_generated_count: generated,
      bulk_generation_completed_at: Math.floor(Date.now() / 1000),
    })
    .eq('id', campaign_id);

  return { generated };
}

export async function POST(req: NextRequest) {
  try {
    const authHeader = req.headers.get('authorization') || '';
    const serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    const pushServer = process.env.NEXT_PUBLIC_PUSH_SERVER || process.env.NEXT_PUBLIC_BACKEND_URL;

    if (!serviceKey) {
      return NextResponse.json({ error: 'Service key not configured' }, { status: 500 });
    }

    const body = await req.json();

    // Try upstream first if configured
    if (pushServer) {
      const upstreamURL = `${pushServer}/content-generation/trigger`;
      console.log('[content-generation/trigger] Forwarding to', upstreamURL, 'with action:', body?.action);
      try {
        const res = await fetch(upstreamURL, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            // Forward auth header in case upstream requires it
            'Authorization': authHeader,
          },
          body: JSON.stringify(body),
        });

        const text = await res.text();
        if (res.ok) {
          console.log('[content-generation/trigger] Upstream success', res.status);
          return new NextResponse(text, { status: res.status, headers: { 'Content-Type': 'application/json' } });
        }
        console.error('[content-generation/trigger] Upstream failed', res.status, text);
        // Fall through to local fallback below
      } catch (e: any) {
        console.error('[content-generation/trigger] Upstream fetch error, using fallback:', e?.message || e);
        // Fall through to local fallback below
      }
    } else {
      console.warn('[content-generation/trigger] No push server configured, using local fallback');
    }

    // Local fallback generation
    if (body?.action === 'bulk_campaign_generation' && body?.campaign_id && body?.company_id) {
      const result = await runLocalBulkGeneration({ campaign_id: body.campaign_id, company_id: body.company_id });
      return NextResponse.json({ ok: true, fallback: true, ...result }, { status: 200 });
    }

    return NextResponse.json({ error: 'Invalid request or upstream failed' }, { status: 500 });
  } catch (error) {
    console.error('Error in content-generation trigger proxy:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

