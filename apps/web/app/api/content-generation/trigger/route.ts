import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Proxy + fallback bulk content generator.
// 1) Try forwarding to push server
// 2) If upstream missing or fails, run a minimal local bulk generator using existing APIs

async function runLocalBulkGeneration(params: { campaign_id: string; company_id: string }) {
  const { campaign_id, company_id } = params;
  const url = process.env.NEXT_PUBLIC_SUPABASE_URL!;
  const serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
  const supabase = createClient(url, serviceKey, { auth: { persistSession: false } });

  // Mark campaign as in_progress
  await supabase.from('company_campaigns').update({ bulk_generation_status: 'in_progress' }).eq('id', campaign_id);

  // Fetch pending tasks (status pending or null)
  const { data: tasks, error: fetchErr } = await supabase
    .from('company_content')
    .select('id, content_type, channel, language, task_title, task_description')
    .eq('campaign_id', campaign_id)
    .or('content_generation_status.is.null,content_generation_status.eq.pending')
    .limit(1000);

  if (fetchErr) throw fetchErr;
  if (!tasks || tasks.length === 0) return { generated: 0 };

  let generated = 0;

  for (const t of tasks) {
    // Set status to generating
    await supabase.from('company_content').update({ content_generation_status: 'generating', is_generating: true }).eq('id', t.id);

    try {
      // Use existing API that generates content for a template/channel
      const res = await fetch(`${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/api/ai/generate-template-channel-content`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          company_id,
          content_type: t.content_type,
          channel: t.channel,
          language: t.language || 'en',
          title: t.task_title,
          description: t.task_description,
        }),
      });

      if (!res.ok) throw new Error(`content api failed ${res.status}`);
      const payload = await res.json();

      // Ensure text for content column
      const contentText = typeof payload?.content === 'string'
        ? payload.content
        : JSON.stringify(payload?.content ?? payload);

      await supabase
        .from('company_content')
        .update({
          content: contentText,
          content_generation_status: 'completed',
          is_generating: false,
          content_generation_attempts: (1),
        })
        .eq('id', t.id);

      generated += 1;
    } catch (e: any) {
      await supabase
        .from('company_content')
        .update({
          content_generation_status: 'failed',
          content_generation_error: String(e?.message || e),
          is_generating: false,
          content_generation_attempts: (1),
        })
        .eq('id', t.id);
    }
  }

  // Update campaign counters
  await supabase
    .from('company_campaigns')
    .update({
      bulk_generation_status: 'completed',
      tasks_content_generated_count: generated,
      bulk_generation_completed_at: Math.floor(Date.now() / 1000),
    })
    .eq('id', campaign_id);

  return { generated };
}

export async function POST(req: NextRequest) {
  try {
    const authHeader = req.headers.get('authorization') || '';
    const serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    const pushServer = process.env.NEXT_PUBLIC_PUSH_SERVER || process.env.NEXT_PUBLIC_BACKEND_URL;

    if (!serviceKey) {
      return NextResponse.json({ error: 'Service key not configured' }, { status: 500 });
    }

    // Only allow calls from server with the service role key
    if (!authHeader.includes(serviceKey)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await req.json();

    // Try upstream first if configured
    if (pushServer) {
      const upstreamURL = `${pushServer}/content-generation/trigger`;
      console.log('[content-generation/trigger] Forwarding to', upstreamURL, 'with action:', body?.action);
      const res = await fetch(upstreamURL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          // Forward auth header in case upstream requires it
          'Authorization': authHeader,
        },
        body: JSON.stringify(body),
      });

      const text = await res.text();
      if (res.ok) {
        console.log('[content-generation/trigger] Upstream success', res.status);
        return new NextResponse(text, { status: res.status, headers: { 'Content-Type': 'application/json' } });
      }
      console.error('[content-generation/trigger] Upstream failed', res.status, text);
      // Fall back to local generation below
    } else {
      console.warn('[content-generation/trigger] No push server configured, using local fallback');
    }

    // Local fallback generation
    if (body?.action === 'bulk_campaign_generation' && body?.campaign_id && body?.company_id) {
      const result = await runLocalBulkGeneration({ campaign_id: body.campaign_id, company_id: body.company_id });
      return NextResponse.json({ ok: true, fallback: true, ...result }, { status: 200 });
    }

    return NextResponse.json({ error: 'Invalid request or upstream failed' }, { status: 500 });
  } catch (error) {
    console.error('Error in content-generation trigger proxy:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

