import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const client = getSupabaseServerClient();
    const { data: authData, error: authError } = await client.auth.getUser();
    const user = authData.user;
    
    if (!user || authError) {
      return NextResponse.json({ error: 'User not found' }, { status: 401 });
    }

    const body = await request.json();
    const { campaign_id, company_id } = body;

    if (!campaign_id || !company_id) {
      return NextResponse.json({ 
        error: 'Missing required fields: campaign_id and company_id' 
      }, { status: 400 });
    }

    // Trigger content generation
    const pushServer = process.env.NEXT_PUBLIC_PUSH_SERVER || process.env.NEXT_PUBLIC_BACKEND_URL;
    
    if (!pushServer) {
      return NextResponse.json({ 
        error: 'Content generation service not configured' 
      }, { status: 500 });
    }

    const response = await fetch(`${pushServer}/content-generation/trigger`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`,
      },
      body: JSON.stringify({
        action: 'start',
        campaign_id,
        company_id,
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Content generation trigger failed: ${response.status} - ${errorText}`);
    }

    const result = await response.json();

    return NextResponse.json({
      success: true,
      message: 'Content generation triggered successfully',
      result
    });

  } catch (error) {
    console.error('Error in auto-trigger content:', error);
    return NextResponse.json({
      error: 'Failed to trigger content generation',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
